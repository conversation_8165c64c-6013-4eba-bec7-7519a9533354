import React, { useState } from 'react';
import { Box, Container, Typography, Button, Stack, Paper } from '@mui/material';
import { ToolConfigDialog } from 'src/sections/agents/clone-template/tool-config-dialog';

// ----------------------------------------------------------------------

export default function TestGoogleAuth() {
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  return (
    <Container maxWidth="md" sx={{ py: 5 }}>
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom>
          Google Account Selector Test
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Click the button below to test the Google account selection dialog
        </Typography>

        <Stack spacing={2} alignItems="center">
          <Button
            variant="contained"
            size="large"
            onClick={handleOpenDialog}
            sx={{ minWidth: 200 }}
          >
            Open Google Account Selector
          </Button>
        </Stack>
      </Paper>

      <ToolConfigDialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        toolName="Gmail"
        toolIcon="logos:gmail"
        connectedAccounts={[]}
      />
    </Container>
  );
}
