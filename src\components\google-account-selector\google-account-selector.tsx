import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogA<PERSON>,
  <PERSON>,
  Typography,
  Button,
  Stack,
  Avatar,
  useTheme,
  IconButton,
} from '@mui/material';
// import { GoogleLogin, GoogleOAuthProvider } from '@react-oauth/google';
// import { jwtDecode } from 'jwt-decode';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

interface Google<PERSON>ser {
  id: string;
  email: string;
  name: string;
  picture?: string;
  given_name?: string;
  family_name?: string;
}

interface GoogleAccountSelectorProps {
  open: boolean;
  onClose: () => void;
  onAccountSelect: (user: GoogleUser) => void;
  selectedAccounts?: GoogleUser[];
}

// ----------------------------------------------------------------------

const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || '';

export function GoogleAccountSelector({
  open,
  onClose,
  onAccountSelect,
  selectedAccounts = [],
}: GoogleAccountSelectorProps) {
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Mock Google accounts for demonstration
  const mockGoogleAccounts = [
    {
      id: 'google_1',
      email: '<EMAIL>',
      name: 'Bobby DeSimone',
      picture: '',
    },
    {
      id: 'google_2',
      email: '<EMAIL>',
      name: 'Jane Smith',
      picture: '',
    },
    {
      id: 'google_3',
      email: '<EMAIL>',
      name: 'Mike Johnson',
      picture: '',
    },
  ];

  const handleMockAccountSelect = (account: GoogleUser) => {
    try {
      setIsLoading(true);
      setError(null);

      // Check if account is already selected
      const isAlreadySelected = selectedAccounts.some((acc) => acc.email === account.email);

      if (isAlreadySelected) {
        setError('This account is already connected');
        return;
      }

      onAccountSelect(account);
      onClose();
    } catch (err: any) {
      console.error('Account selection error:', err);
      setError(err.message || 'Failed to select account');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setError(null);
    onClose();
  };

  if (!GOOGLE_CLIENT_ID) {
    return (
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogContent>
          <Box textAlign="center" py={3}>
            <Typography color="error" variant="h6">
              Google Client ID not configured
            </Typography>
            <Typography color="text.secondary" sx={{ mt: 1 }}>
              Please add VITE_GOOGLE_CLIENT_ID to your environment variables
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 0,
          maxWidth: 450,
        },
      }}
    >
      {/* Google Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 1,
          p: 3,
          pb: 2,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <Iconify icon="logos:google" width={24} height={24} />
          <Typography variant="h6" fontWeight={400} color="text.secondary">
            Sign in with Google
          </Typography>
        </Box>
        <IconButton onClick={handleClose} size="small">
          <Iconify icon="eva:close-fill" />
        </IconButton>
      </Box>

      <DialogContent sx={{ p: 3, pt: 2 }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Typography variant="h5" fontWeight={400} sx={{ mb: 1 }}>
            Choose an account
          </Typography>
          <Typography variant="body2" color="text.secondary">
            to continue to <span style={{ color: '#1976d2' }}>beyondperimeter.com</span>
          </Typography>
        </Box>

        {error && (
          <Box
            sx={{
              p: 2,
              mb: 2,
              backgroundColor: theme.palette.error.lighter,
              borderRadius: 1,
              border: `1px solid ${theme.palette.error.light}`,
            }}
          >
            <Typography variant="body2" color="error">
              {error}
            </Typography>
          </Box>
        )}

        <Stack spacing={1}>
          {mockGoogleAccounts.map((account) => (
            <Box
              key={account.id}
              onClick={() => handleMockAccountSelect(account)}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                p: 2,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                '&:hover': {
                  backgroundColor: theme.palette.action.hover,
                  borderColor: theme.palette.primary.main,
                },
              }}
            >
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: '50%',
                  backgroundColor: '#4285f4',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 500,
                  fontSize: '14px',
                }}
              >
                {account.name.charAt(0)}
              </Box>
              <Box flex={1}>
                <Typography variant="body1" fontWeight={500} sx={{ fontSize: '14px' }}>
                  {account.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '13px' }}>
                  {account.email}
                </Typography>
              </Box>
            </Box>
          ))}

          {/* Use another account option */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              p: 2,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 1,
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
                borderColor: theme.palette.primary.main,
              },
            }}
          >
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                backgroundColor: theme.palette.grey[400],
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Iconify icon="eva:person-outline" width={18} height={18} color="white" />
            </Box>
            <Typography variant="body1" fontWeight={500} sx={{ fontSize: '14px' }}>
              Use another account
            </Typography>
          </Box>
        </Stack>

        {/* Privacy Notice */}
        <Box sx={{ mt: 3, p: 2, backgroundColor: theme.palette.grey[50], borderRadius: 1 }}>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ fontSize: '12px', lineHeight: 1.4 }}
          >
            To continue, Google will share your name, email address, language preference, and
            profile picture with beyondperimeter.com.
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1, justifyContent: 'flex-start' }}>
        <Button onClick={handleClose} color="inherit" sx={{ textTransform: 'none' }}>
          ← Back
        </Button>
      </DialogActions>
    </Dialog>
  );
}
