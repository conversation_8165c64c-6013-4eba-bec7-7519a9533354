import 'src/global.css';
import './i18n'; // Import i18n configuration

// ----------------------------------------------------------------------

import { Router } from 'src/routes/sections';

import { useScrollToTop } from 'src/hooks/use-scroll-to-top';

import { ThemeProvider } from 'src/theme/theme-provider';

import { ProgressBar } from 'src/components/progress-bar';
import { MotionLazy } from 'src/components/animate/motion-lazy';
import { SettingsDrawer, defaultSettings, SettingsProvider } from 'src/components/settings';
import { GlobalAccountMenu } from 'src/components/global-account-menu';
import { AccountMenuProvider } from 'src/contexts/account-menu-context';

import { AuthProvider } from 'src/auth/context/jwt';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import SnackbarProvider from './components/snackbar/snackbar-provider';
import { DeveloperModeProvider } from './contexts/developer-mode-context';
import { GoogleOAuthProvider } from '@react-oauth/google';

// ----------------------------------------------------------------------

export default function App() {
  useScrollToTop();
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        retry: false,
      },
    },
  });

  const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || '';

  return (
    <AuthProvider>
      <DeveloperModeProvider>
        <QueryClientProvider client={queryClient}>
          <SettingsProvider settings={defaultSettings}>
            <ThemeProvider>
              <AccountMenuProvider>
                <MotionLazy>
                  <SnackbarProvider>
                    <ProgressBar />
                    <SettingsDrawer />
                    <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
                      <Router />
                    </GoogleOAuthProvider>
                  </SnackbarProvider>
                </MotionLazy>
              </AccountMenuProvider>
            </ThemeProvider>
          </SettingsProvider>
        </QueryClientProvider>
      </DeveloperModeProvider>
    </AuthProvider>
  );
}
