import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Agentss
export const AgentsEndpoints = {
  list: '/Agents',
  details: '/Agents',
};

// Define the Category interface
export interface Agents {
  Agents: any;
}
export interface AgentsResponse {
  Agents: any;
}

// Define the API response structure

// Create a hook to use the Agentss API
export const useAgentsApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Agentss
  const useGetAgentss = () => {
    return apiServices.useGetListService<AgentsResponse>({
      url: AgentsEndpoints.list,
    });
  };

  // Get a single Agents by ID
  const useGetAgents = (id: string) => {
    return apiServices.useGetItemService<Agents>({
      url: AgentsEndpoints.details,
      id,
    });
  };

  // Create a new Agents
  const useCreateAgents = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<Agents, any>({
      url: AgentsEndpoints.list,
      onSuccess,
    });
  };

  // Update a Agents
  const useUpdateAgents = (id: string, onSuccess?: () => void) => {
    return apiServices.usePutService<Agents>({
      url: AgentsEndpoints.details,
      id,
      onSuccess,
    });
  };

  // Delete a Agents
  const useDeleteAgents = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any>({
      url: AgentsEndpoints.details,
      onSuccess,
    });
  };

  return {
    useGetAgentss,
    useGetAgents,
    useCreateAgents,
    useUpdateAgents,
    useDeleteAgents,
  };
};
