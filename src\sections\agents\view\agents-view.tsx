import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Avatar,
  Chip,
  InputAdornment,
  useTheme,
  Grid,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  <PERSON><PERSON>,
  Divider,
} from '@mui/material';
import { Icon } from '@iconify/react';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { useAgentView, Template } from './use-agent-view';

// ----------------------------------------------------------------------

// Agent Card Component
const AgentCard = ({ agent, id }: { agent: Template; id: number }) => {
  const theme = useTheme();
  const router = useRouter();

  const handleCloneTemplate = (id: number) => {
    // Navigate to the conversation chat page
    router.push(paths.dashboard.agents.clone?.(id));
  };

  return (
    <Card
      sx={{
        p: 3,
        height: '100%',
        border: '1px solid',
        borderColor: 'divider',
        background: (theme) =>
          `linear-gradient(145deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
        backdropFilter: 'blur(10px)',
        borderRadius: 3,
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '3px',
          background: (theme) =>
            `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 50%, ${theme.palette.error.main} 100%)`,
          opacity: 0,
          transition: 'opacity 0.3s ease',
        },
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: (theme) => `${theme.shadows[8]}, 0 0 0 1px ${theme.palette.primary.main}20`,
          borderColor: 'primary.main',
          '&::before': {
            opacity: 1,
          },
        },
      }}
    >
      <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" fontWeight={600} color="text.primary">
              {agent.name}
            </Typography>
            <Chip
              label={agent.type}
              size="small"
              sx={{
                bgcolor: ' rgba(163, 139, 233, 0.33)',
                color: 'inherit',
                fontWeight: 600,
                fontSize: '0.75rem',
                letterSpacing: '0.025em',
                border: (theme) => `1px solid ${theme.palette.primary.main}40`,
                borderRadius: '6px',
                height: '24px',
                '& .MuiChip-label': {
                  px: 1.5,
                },
                '&:hover': {
                  bgcolor: (theme) => theme.palette.primary.main + '10',
                  color: 'primary.main',
                  transform: 'scale(1.05)',
                },
              }}
            />
          </Box>
          <IconButton
            size="small"
            sx={{
              color: 'text.secondary',
              borderRadius: '8px',
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: (theme) => theme.palette.primary.main + '10',
                color: 'primary.main',
                transform: 'scale(1.05)',
              },
            }}
          >
            <Icon icon="eva:more-horizontal-fill" width={18} height={18} />
          </IconButton>
        </Box>

        {/* Model Information */}
        <Box display="flex" alignItems="center" gap={1} mb={2}>
          <Typography variant="body2" color="text.secondary" fontWeight={500}>
            Model:
          </Typography>
          <Chip
            label={agent.model.replace(/_/g, ' ')}
            size="small"
            sx={{
              bgcolor: 'main',
              fontSize: '0.7rem',
              height: '20px',
              '& .MuiChip-label': {
                px: 1,
              },
            }}
          />
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, lineHeight: 1.4 }}>
          {agent.description}
        </Typography>

        {/* Category Information */}
        <Box display="flex" alignItems="center" gap={1} mb={3}>
          <Avatar
            sx={{
              width: 44,
              height: 44,
              bgcolor: agent.category.theme + '20',
              border: '1px solid',
              borderColor: agent.category.theme + '40',
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'scale(1.1)',
                bgcolor: agent.category.theme + '30',
                borderColor: agent.category.theme,
              },
            }}
          >
            <Icon
              icon={agent.category.icon}
              width={24}
              height={24}
              style={{ color: agent.category.theme }}
            />
          </Avatar>
          <Box>
            <Typography
              variant="body2"
              fontWeight={600}
              color="text.primary"
              sx={{ textTransform: 'capitalize' }}
            >
              {agent.category.name}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {agent.category.description}
            </Typography>
          </Box>
        </Box>

        <AppButton
          variant="outlined"
          color="primary"
          fullWidth
          onClick={() => handleCloneTemplate?.(id)}
          label=" Clone Template"
        />
      </CardContent>
    </Card>
  );
};

export function AgentsView() {
  // Use the agent view hook to manage state and data
  const {
    filteredAgents,
    isLoading,
    error,
    refetch,
    searchQuery,
    selectedTypeTab,
    selectedCategoryTab,
    TYPE_FILTERS,
    CATEGORY_FILTERS,
    handleSearch,
    handleTypeTabChange,
    handleCategoryTabChange,
  } = useAgentView();

  // Show loading state
  if (isLoading) {
    return (
      <Box
        sx={{
          width: '100%',
          minHeight: '100vh',
          bgcolor: 'background.default',
          p: { xs: 2, sm: 3, md: 4 },
          maxWidth: '1400px',
          mx: 'auto',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress size={40} />
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Box
        sx={{
          width: '100%',
          minHeight: '100vh',
          bgcolor: 'background.default',
          p: { xs: 2, sm: 3, md: 4 },
          maxWidth: '1400px',
          mx: 'auto',
        }}
      >
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load templates. Please try again.
        </Alert>
        <AppButton variant="outlined" onClick={() => refetch()} label="Retry" />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '100vh',
        bgcolor: 'background.default',
        p: { xs: 2, sm: 3, md: 4 },
        maxWidth: '1400px',
        mx: 'auto',
      }}
    >
      {/* Header Section */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
        sx={{ position: 'relative', zIndex: 1 }}
      >
        <Box>
          <Typography variant="h3" fontWeight={800} color="text.primary">
            Teams
          </Typography>
          <Typography sx={{ color: 'rgba(15, 14, 17, 0.65)' }}>
            Enable advanced workflows with applications
          </Typography>
        </Box>
        <AppButton
          variant="contained"
          startIcon={<Icon icon="eva:plus-fill" width={20} height={20} />}
          label="New Agent's Template"
          fullWidth={false}
        />
      </Box>
      <Divider sx={{ mb: 2 }} />

      {/* Agents Templates Section */}
      <Box sx={{ mb: 6 }}>
        {/* Filter Section */}
        <Box sx={{ mb: 6, width: '100%' }}>
          {/* Type Filter Tabs - Above Search */}
          <Box sx={{ mb: 3 }}>
            <Box
              sx={{
                display: 'inline-flex',
                bgcolor: 'rgba(24, 0, 72, 0.08)',
                height: 36,
                borderRadius: '10px',
              }}
            >
              <Tabs
                value={selectedTypeTab}
                onChange={handleTypeTabChange}
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  '& .MuiTab-root': {
                    textTransform: 'none',
                    fontWeight: 500,
                    minHeight: 36,
                    height: 36,

                    px: 3,

                    py: 1,
                    minWidth: 'auto',
                    border: '1px solid ',
                    borderColor: 'transparent',
                    bgcolor: 'transparent',
                    color: 'text.secondary',
                    transition: 'all 0.2s ease',
                    '&.Mui-selected': {
                      color: 'inherit',
                      borderColor: 'rgba(24, 0, 72, 0.08)',
                      fontWeight: 600,
                      borderRadius: '10px',
                    },
                    '&:hover': {
                      bgcolor: 'action.hover',
                    },
                  },
                  '& .MuiTabs-indicator': {
                    display: 'none',
                  },
                  '& .MuiTabs-flexContainer': {
                    gap: 0,
                  },
                }}
              >
                {TYPE_FILTERS.map((filter, index) => (
                  <Tab key={index} label={filter} />
                ))}
              </Tabs>
            </Box>
          </Box>

          {/* Search Bar */}
          <Box sx={{ mb: 3, width: '100%' }}>
            <TextField
              fullWidth
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:search-fill" width={20} sx={{ color: 'inherit' }} />
                  </InputAdornment>
                ),
              }}
              sx={{
                width: '100%',
                '& .MuiOutlinedInput-root': {
                  borderColor: 'transparent',
                  borderRadius: 1,
                  bgcolor: 'rgba(24, 0, 72, 0.08)',
                  height: 48,
                  fontSize: '0.95rem',
                  '&:hover': {
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                    },
                  },
                  '&.Mui-focused': {
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                      borderWidth: 2,
                    },
                  },
                },
              }}
            />
          </Box>

          {/* Category Filter Tabs - Below Search */}
          <Box>
            <Tabs
              value={selectedCategoryTab}
              onChange={handleCategoryTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  textTransform: 'none',
                  fontWeight: 500,
                  minHeight: 36,
                  height: 36,
                  px: 3,
                  py: 1,
                  minWidth: 'auto',
                  borderRadius: '20px',
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'rgba(24, 0, 72, 0.08)',
                  color: 'text.secondary',
                  transition: 'all 0.2s ease',
                  '&.Mui-selected': {
                    color: 'inherit',
                    bgcolor: 'rgba(163, 139, 233, 0.33)',
                    borderColor: 'primary.main',
                    fontWeight: 600,
                  },
                  '&:hover': {
                    bgcolor: 'action.hover',
                  },
                },
                '& .MuiTabs-indicator': {
                  display: 'none',
                },
                '& .MuiTabs-flexContainer': {
                  gap: 1,
                },
              }}
            >
              {CATEGORY_FILTERS.map((filter, index) => (
                <Tab key={index} label={filter} />
              ))}
            </Tabs>
          </Box>
        </Box>

        {/* Agents Grid */}
        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
          {filteredAgents.map((agent) => (
            <Grid item xs={12} sm={6} lg={4} key={agent.id}>
              <AgentCard agent={agent} id={agent?.id} />
            </Grid>
          ))}
        </Grid>

        {/* No Results */}
        {filteredAgents.length === 0 && (
          <Box
            sx={{
              textAlign: 'center',
              py: 12,
              px: 4,
            }}
          >
            <Box
              sx={{
                width: 120,
                height: 120,
                mx: 'auto',
                mb: 3,
                borderRadius: '50%',
                bgcolor: 'action.hover',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Iconify
                icon="eva:search-outline"
                width={48}
                height={48}
                sx={{ color: 'text.disabled' }}
              />
            </Box>
            <Typography variant="h5" color="text.primary" sx={{ mb: 2, fontWeight: 600 }}>
              No agents found
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto' }}>
              Try adjusting your search or filter criteria to find the agents you&apos;re looking
              for
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
}
