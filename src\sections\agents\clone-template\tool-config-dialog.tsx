import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Button,
  IconButton,
  Stack,
  Divider,
  Radio,
  useTheme,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common/app-button';

// ----------------------------------------------------------------------

interface ConnectedAccount {
  id: string;
  email: string;
  provider: string;
  icon: string;
}

interface ToolConfigDialogProps {
  open: boolean;
  onClose: () => void;
  toolName: string;
  toolIcon: string;
  connectedAccounts?: ConnectedAccount[];
}

export function ToolConfigDialog({
  open,
  onClose,
  toolName,
  toolIcon,
  connectedAccounts = [],
}: ToolConfigDialogProps) {
  const theme = useTheme();
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [showAccountSelector, setShowAccountSelector] = useState(false);
  const [localConnectedAccounts, setLocalConnectedAccounts] =
    useState<ConnectedAccount[]>(connectedAccounts);

  // Mock Google accounts for demonstration
  const mockGoogleAccounts = [
    {
      id: 'google_1',
      email: '<EMAIL>',
      name: 'Bobby DeSimone',
      avatar: '/assets/images/avatar/avatar-1.jpg',
    },
    {
      id: 'google_2',
      email: '<EMAIL>',
      name: 'Jane Smith',
      avatar: '/assets/images/avatar/avatar-2.jpg',
    },
    {
      id: 'google_3',
      email: '<EMAIL>',
      name: 'Mike Johnson',
      avatar: '/assets/images/avatar/avatar-3.jpg',
    },
  ];

  const handleAddNewAccount = () => {
    setShowAccountSelector(true);
  };

  const handleAccountSelect = (account: any) => {
    // Add the selected account to the connected accounts list
    const newAccount: ConnectedAccount = {
      id: account.id,
      email: account.email,
      provider: 'Google',
      icon: 'logos:google',
    };

    // Check if account is already connected
    const isAlreadyConnected = localConnectedAccounts.some((acc) => acc.email === account.email);

    if (!isAlreadyConnected) {
      setLocalConnectedAccounts((prev) => [...prev, newAccount]);
    }

    // Select the account
    setSelectedAccount(account.id);
    setShowAccountSelector(false);
  };

  const handleContinue = () => {
    // Handle continue logic here
    onClose();
  };

  const handleCancel = () => {
    setShowAccountSelector(false);
    setSelectedAccount('');
    onClose();
  };

  if (showAccountSelector) {
    return (
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            p: 0,
            maxWidth: 450,
          },
        }}
      >
        {/* Google Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            p: 3,
            pb: 2,
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Iconify icon="logos:google" width={24} height={24} />
          <Typography variant="h6" fontWeight={400} color="text.secondary">
            Sign in with Google
          </Typography>
        </Box>

        <DialogContent sx={{ p: 3, pt: 2 }}>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography variant="h5" fontWeight={400} sx={{ mb: 1 }}>
              Choose an account
            </Typography>
            <Typography variant="body2" color="text.secondary">
              to continue to <span style={{ color: '#1976d2' }}>beyondperimeter.com</span>
            </Typography>
          </Box>

          <Stack spacing={1}>
            {mockGoogleAccounts.map((account) => (
              <Box
                key={account.id}
                onClick={() => handleAccountSelect(account)}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  p: 2,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 1,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    backgroundColor: theme.palette.action.hover,
                    borderColor: theme.palette.primary.main,
                  },
                }}
              >
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    backgroundColor: '#4285f4',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontWeight: 500,
                    fontSize: '14px',
                  }}
                >
                  {account.name.charAt(0)}
                </Box>
                <Box flex={1}>
                  <Typography variant="body1" fontWeight={500} sx={{ fontSize: '14px' }}>
                    {account.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: '13px' }}>
                    {account.email}
                  </Typography>
                </Box>
              </Box>
            ))}

            {/* Use another account option */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                p: 2,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                '&:hover': {
                  backgroundColor: theme.palette.action.hover,
                  borderColor: theme.palette.primary.main,
                },
              }}
            >
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: '50%',
                  backgroundColor: theme.palette.grey[400],
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Iconify icon="eva:person-outline" width={18} height={18} color="white" />
              </Box>
              <Typography variant="body1" fontWeight={500} sx={{ fontSize: '14px' }}>
                Use another account
              </Typography>
            </Box>
          </Stack>

          {/* Privacy Notice */}
          <Box sx={{ mt: 3, p: 2, backgroundColor: theme.palette.grey[50], borderRadius: 1 }}>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ fontSize: '12px', lineHeight: 1.4 }}
            >
              To continue, Google will share your name, email address, language preference, and
              profile picture with beyondperimeter.com.
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 1, justifyContent: 'flex-start' }}>
          <Button
            onClick={() => setShowAccountSelector(false)}
            color="inherit"
            sx={{ textTransform: 'none' }}
          >
            ← Back
          </Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          background: '#F0F0F1',
        },
      }}
    >
      <DialogTitle sx={{ pb: 2 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6" fontWeight={600}>
            Connect {toolName}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <Iconify icon="eva:close-fill" />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ py: 0 }}>
        <Stack spacing={3}>
          <Typography variant="body2" fontWeight={500} color="text.primary">
            Connected accounts
          </Typography>

          {/* Connected Accounts List */}
          <Stack spacing={2}>
            {localConnectedAccounts.length > 0 ? (
              localConnectedAccounts.map((account) => (
                <Box
                  key={account.id}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: 2,
                    border: `1px solid ${theme.palette.divider}`,
                    borderRadius: 1,
                  }}
                >
                  <Box display="flex" alignItems="center" gap={2}>
                    <Iconify icon={account.icon} width={24} height={24} />
                    <Typography variant="body2">{account.email}</Typography>
                  </Box>
                  <Radio
                    checked={selectedAccount === account.id}
                    onChange={() => setSelectedAccount(account.id)}
                    size="small"
                  />
                </Box>
              ))
            ) : (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ textAlign: 'center', py: 2 }}
              >
                No accounts connected yet. Click "Add new account" to get started.
              </Typography>
            )}

            {/* Add New Account Button */}
            <AppButton
              fullWidth={false}
              variant="contained"
              startIcon={<Iconify icon="eva:plus-fill" />}
              onClick={handleAddNewAccount}
              sx={{
                justifyContent: 'flex-start',
                width: '33%',
                color: 'text.secondary',
                background: 'white',
                borderColor: '#CBC9CF',
                '&:hover': {
                  borderColor: theme.palette.primary.main,
                  backgroundColor: theme.palette.action.hover,
                },
              }}
              label="Add new account"
            />
          </Stack>
        </Stack>
      </DialogContent>

      <DialogActions
        sx={{
          p: 3,
          pt: 2,
          gap: 2,
          background: '#E7E6E9',
          borderTop: '1px solid #CDCAD5',
          mt: '24px',
        }}
      >
        <Button onClick={handleCancel} color="inherit" variant="outlined">
          Cancel
        </Button>
        <AppButton
          label="Continue"
          variant="contained"
          color="primary"
          fullWidth={false}
          onClick={handleContinue}
          sx={{
            minWidth: 100,
            backgroundColor: '#9C6FE4',
            '&:hover': {
              backgroundColor: '#8B5CF6',
            },
          }}
        />
      </DialogActions>
    </Dialog>
  );
}
