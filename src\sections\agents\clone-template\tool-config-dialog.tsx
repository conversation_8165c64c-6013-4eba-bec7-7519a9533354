import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Button,
  IconButton,
  Stack,
  Divider,
  Radio,
  useTheme,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common/app-button';

// ----------------------------------------------------------------------

interface ConnectedAccount {
  id: string;
  email: string;
  provider: string;
  icon: string;
}

interface ToolConfigDialogProps {
  open: boolean;
  onClose: () => void;
  toolName: string;
  toolIcon: string;
  connectedAccounts?: ConnectedAccount[];
}

export function ToolConfigDialog({
  open,
  onClose,
  toolName,
  toolIcon,
  connectedAccounts = [],
}: ToolConfigDialogProps) {
  const theme = useTheme();
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [showAccountSelector, setShowAccountSelector] = useState(false);

  // Mock Google accounts for demonstration
  const mockGoogleAccounts = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'John Doe',
      avatar: '/assets/images/avatar/avatar-1.jpg',
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: 'Jane Smith',
      avatar: '/assets/images/avatar/avatar-2.jpg',
    },
    {
      id: '3',
      email: '<EMAIL>',
      name: 'Mike Johnson',
      avatar: '/assets/images/avatar/avatar-3.jpg',
    },
  ];

  const handleAddNewAccount = () => {
    setShowAccountSelector(true);
  };

  const handleAccountSelect = (accountId: string) => {
    setSelectedAccount(accountId);
    setShowAccountSelector(false);
    // Here you would typically handle the account connection logic
  };

  const handleContinue = () => {
    // Handle continue logic here
    onClose();
  };

  const handleCancel = () => {
    setShowAccountSelector(false);
    setSelectedAccount('');
    onClose();
  };

  if (showAccountSelector) {
    return (
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            p: 1,
          },
        }}
      >
        <DialogTitle sx={{ pb: 2 }}>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="h6" fontWeight={600}>
              Choose Google Account
            </Typography>
            <IconButton onClick={onClose} size="small">
              <Iconify icon="eva:close-fill" />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ py: 0 }}>
          <Stack spacing={2}>
            {mockGoogleAccounts.map((account) => (
              <Box
                key={account.id}
                onClick={() => handleAccountSelect(account.id)}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  p: 2,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 1,
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: theme.palette.action.hover,
                  },
                }}
              >
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontWeight: 600,
                  }}
                >
                  {account.name.charAt(0)}
                </Box>
                <Box flex={1}>
                  <Typography variant="body2" fontWeight={500}>
                    {account.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {account.email}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Stack>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button onClick={() => setShowAccountSelector(false)} color="inherit">
            Back
          </Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          background: '#F0F0F1',
        },
      }}
    >
      <DialogTitle sx={{ pb: 2 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6" fontWeight={600}>
            Connect {toolName}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <Iconify icon="eva:close-fill" />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ py: 0 }}>
        <Stack spacing={3}>
          <Typography variant="body2" fontWeight={500} color="text.primary">
            Connected accounts
          </Typography>

          {/* Connected Accounts List */}
          <Stack spacing={2}>
            {connectedAccounts.length > 0 ? (
              connectedAccounts.map((account) => (
                <Box
                  key={account.id}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: 2,
                    border: `1px solid ${theme.palette.divider}`,
                    borderRadius: 1,
                  }}
                >
                  <Box display="flex" alignItems="center" gap={2}>
                    <Iconify icon={account.icon} width={24} height={24} />
                    <Typography variant="body2">{account.email}</Typography>
                  </Box>
                  <Radio
                    checked={selectedAccount === account.id}
                    onChange={() => setSelectedAccount(account.id)}
                    size="small"
                  />
                </Box>
              ))
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 2,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 1,
                }}
              >
                <Box display="flex" alignItems="center" gap={2}>
                  <Iconify icon="logos:google" width={24} height={24} />
                  <Typography variant="body2"><EMAIL></Typography>
                </Box>
                <Radio
                  checked={selectedAccount === 'default'}
                  onChange={() => setSelectedAccount('default')}
                  size="small"
                />
              </Box>
            )}

            {/* Add New Account Button */}
            <Button
              variant="outlined"
              startIcon={<Iconify icon="eva:plus-fill" />}
              onClick={handleAddNewAccount}
              sx={{
                justifyContent: 'flex-start',
                color: 'text.secondary',
                borderColor: theme.palette.divider,
                borderStyle: 'dashed',
                py: 1.5,
                '&:hover': {
                  borderColor: theme.palette.primary.main,
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              Add new account
            </Button>
          </Stack>
        </Stack>
      </DialogContent>

      <DialogActions
        sx={{ p: 3, pt: 2, gap: 2, background: '#E7E6E9', borderTop: '1px solid #F0F0F0' }}
      >
        <Button onClick={handleCancel} color="inherit" variant="outlined">
          Cancel
        </Button>
        <AppButton
          label="Continue"
          variant="contained"
          color="primary"
          fullWidth={false}
          onClick={handleContinue}
          sx={{
            minWidth: 100,
            backgroundColor: '#9C6FE4',
            '&:hover': {
              backgroundColor: '#8B5CF6',
            },
          }}
        />
      </DialogActions>
    </Dialog>
  );
}
