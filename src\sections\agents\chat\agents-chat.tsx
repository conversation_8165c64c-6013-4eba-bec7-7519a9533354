import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Avatar,
  AvatarGroup,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Stack,
  Divider,
  useTheme,
  alpha,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

interface ChatHistoryItem {
  id: string;
  title: string;
  taskCount: number;
  status: 'active' | 'completed';
  timestamp: string;
}

interface TaskItem {
  id: string;
  title: string;
  status: 'completed' | 'in-progress';
  agents: Array<{
    id: string;
    name: string;
    avatar: string;
  }>;
  timestamp: string;
}

// Mock data
const CHAT_HISTORY: ChatHistoryItem[] = [
  {
    id: '1',
    title: 'Send an interview invitations',
    taskCount: 4,
    status: 'active',
    timestamp: 'Just Now',
  },
  {
    id: '2',
    title: "Send a follow-up to candidates who didn't reply in 3 days",
    taskCount: 0,
    status: 'completed',
    timestamp: 'Just Now',
  },
  {
    id: '3',
    title: 'Send an interview invitations',
    taskCount: 1,
    status: 'completed',
    timestamp: 'Just Now',
  },
  {
    id: '4',
    title: 'Summarize the last 10 CVs submitted',
    taskCount: 0,
    status: 'completed',
    timestamp: 'Just Now',
  },
];

const TASKS: TaskItem[] = [
  {
    id: '1',
    title: 'Send an interview invitation',
    status: 'completed',
    agents: [
      { id: '1', name: 'X Agent', avatar: '/assets/images/avatars/avatar_1.jpg' },
      { id: '2', name: 'S Agent', avatar: '/assets/images/avatars/avatar_2.jpg' },
      { id: '3', name: 'G Agent', avatar: '/assets/images/avatars/avatar_3.jpg' },
    ],
    timestamp: 'Now',
  },
  {
    id: '2',
    title: 'Send an interview invitation',
    status: 'completed',
    agents: [
      { id: '1', name: 'X Agent', avatar: '/assets/images/avatars/avatar_1.jpg' },
      { id: '2', name: 'S Agent', avatar: '/assets/images/avatars/avatar_2.jpg' },
      { id: '3', name: 'G Agent', avatar: '/assets/images/avatars/avatar_3.jpg' },
    ],
    timestamp: 'Now',
  },
];

const AgentsChat = () => {
  const theme = useTheme();
  const [message, setMessage] = useState('');

  const handleSendMessage = () => {
    if (message.trim()) {
      // Handle sending message
      console.log('Sending message:', message);
      setMessage('');
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        height: '100vh',
        bgcolor: 'background.default',
      }}
    >
      {/* Left Sidebar - Chat History */}
      <Box
        sx={{
          width: 280,
          bgcolor: 'background.paper',
          borderRight: `1px solid ${theme.palette.divider}`,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
          <Box
            sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}
          >
            <Typography variant="h6" fontWeight={600}>
              Chats History
            </Typography>
            <Button
              variant="contained"
              size="small"
              sx={{
                bgcolor: '#9C6FE4',
                '&:hover': { bgcolor: '#8B5CF6' },
                borderRadius: 2,
                textTransform: 'none',
                fontSize: '0.75rem',
                px: 2,
              }}
            >
              New Chat
            </Button>
          </Box>
        </Box>

        {/* Chat History List */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
          {CHAT_HISTORY.map((item) => (
            <Card
              key={item.id}
              sx={{
                p: 2,
                mb: 1,
                cursor: 'pointer',
                border: `1px solid ${theme.palette.divider}`,
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.04),
                },
              }}
            >
              <Typography variant="body2" fontWeight={500} sx={{ mb: 1 }}>
                {item.title}
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="caption" color="text.secondary">
                  {item.taskCount} tasks
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {item.timestamp}
                </Typography>
              </Box>
            </Card>
          ))}
        </Box>
      </Box>

      {/* Main Chat Area */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Chat Header */}
        <Box
          sx={{
            p: 2,
            borderBottom: `1px solid ${theme.palette.divider}`,
            bgcolor: 'background.paper',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton size="small">
              <Iconify icon="eva:arrow-back-fill" />
            </IconButton>
            <Typography variant="h6" fontWeight={600}>
              Team Alpha
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton size="small">
              <Iconify icon="eva:more-vertical-fill" />
            </IconButton>
            <IconButton size="small">
              <Iconify icon="eva:arrow-forward-fill" />
            </IconButton>
          </Box>
        </Box>

        {/* Chat Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          {TASKS.map((task, index) => (
            <Box key={task.id} sx={{ mb: 3 }}>
              {/* Agent Avatars */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 32, height: 32 } }}>
                  {task.agents.map((agent) => (
                    <Avatar
                      key={agent.id}
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: '#333',
                        color: 'white',
                        fontSize: '0.75rem',
                        fontWeight: 600,
                      }}
                    >
                      {agent.name.charAt(0)}
                    </Avatar>
                  ))}
                </AvatarGroup>
                <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                  +3 more
                </Typography>
                <Box sx={{ flex: 1 }} />
                <Typography variant="caption" color="text.secondary">
                  {task.timestamp}
                </Typography>
              </Box>

              {/* Task Card */}
              <Card
                sx={{
                  p: 3,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 2,
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                  }}
                >
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body1" fontWeight={500} sx={{ mb: 2 }}>
                      {task.title}
                    </Typography>
                    <Chip
                      label="Completed"
                      size="small"
                      sx={{
                        bgcolor: alpha('#4CAF50', 0.1),
                        color: '#4CAF50',
                        fontWeight: 500,
                        fontSize: '0.75rem',
                      }}
                    />
                  </Box>
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{
                      textTransform: 'none',
                      borderColor: theme.palette.divider,
                      color: 'text.secondary',
                      '&:hover': {
                        borderColor: theme.palette.primary.main,
                        color: 'primary.main',
                      },
                    }}
                  >
                    View Task Details
                  </Button>
                </Box>
              </Card>
            </Box>
          ))}
        </Box>

        {/* Message Input */}
        <Box
          sx={{
            p: 2,
            borderTop: `1px solid ${theme.palette.divider}`,
            bgcolor: 'background.paper',
          }}
        >
          <TextField
            fullWidth
            placeholder="Ask me anything"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:message-circle-outline" sx={{ color: 'text.secondary' }} />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleSendMessage}
                    disabled={!message.trim()}
                    sx={{
                      bgcolor: message.trim() ? '#9C6FE4' : 'transparent',
                      color: message.trim() ? 'white' : 'text.secondary',
                      '&:hover': {
                        bgcolor: message.trim()
                          ? '#8B5CF6'
                          : alpha(theme.palette.primary.main, 0.04),
                      },
                    }}
                  >
                    <Iconify icon="eva:paper-plane-fill" />
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                borderRadius: 3,
                bgcolor: alpha(theme.palette.grey[500], 0.04),
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
              },
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default AgentsChat;
