import type { Theme, SxProps } from '@mui/material/styles';

import { useTheme } from '@mui/material/styles';

import { Main } from './main';
import { LayoutSection } from '../core/layout-section';

// ----------------------------------------------------------------------

export type DashboardLayoutNoSidebarProps = {
  sx?: SxProps<Theme>;
  children: React.ReactNode;
};

export function DashboardLayoutNoSidebar({ sx, children }: DashboardLayoutNoSidebarProps) {
  const theme = useTheme();

  return (
    <LayoutSection
      /** **************************************
       * Header
       *************************************** */

      /** **************************************
       * Sidebar - Disabled
       *************************************** */
      sidebarSection={null}

      /** **************************************
       * Footer
       *************************************** */
      footerSection={null}

      /** **************************************
       * Style
       *************************************** */
      cssVars={{
        '--layout-transition-easing': 'linear',
        '--layout-transition-duration': '120ms',
        '--layout-dashboard-content-pt': theme.spacing(4),
        '--layout-dashboard-content-pb': theme.spacing(8),
        '--layout-dashboard-content-px': theme.spacing(5),
      }}
      sx={{
        // Remove sidebar padding since there's no sidebar
        ...sx,
      }}
    >
      <Main isNavHorizontal={false}>{children}</Main>
    </LayoutSection>
  );
}
